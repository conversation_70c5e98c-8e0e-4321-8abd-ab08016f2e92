"use client";
import React from 'react';
import ServiceCard from './ServiceCard'; // Import the new ServiceCard component
import { motion } from 'framer-motion';
import { Component as ImageAutoSlider } from '@/components/ui/image-auto-slider';
import { useMobile } from '@/hooks/use-mobile';
import { MobileOptimizedContainer, MobileGrid, MobileText } from '@/components/ui/mobile-optimized-container';
import { useLanguage } from '@/lib/context/language-context';

interface ServiceDataItem {
  title: string;
  description: string;
  href: string;
  imageUrl: string; // Added imageUrl
  delay: number;
}



const ServiceIntroductionSection = () => {
  const { isMobile } = useMobile();
  const { t } = useLanguage();

  const servicesData: ServiceDataItem[] = [
    {
      title: t('services.businessPlanning.title'),
      description: t('services.businessPlanning.description'),
      href: "/services/business-planning",
      imageUrl: "/images/Background/background-of-4.png",
      delay: 0,
    },
    {
      title: t('services.mentorshipNetworking.title'),
      description: t('services.mentorshipNetworking.description'),
      href: "/services/mentorship",
      imageUrl: "/images/Background/background-of-4.png",
      delay: 0.1,
    },
    {
      title: t('services.fundingAccess.title'),
      description: t('services.fundingAccess.description'),
      href: "/services/funding",
      imageUrl: "/images/Background/background-of-4.png",
      delay: 0.2,
    },
    {
      title: t('services.workspaceResources.title'),
      description: t('services.workspaceResources.description'),
      href: "/services/workspace",
      imageUrl: "/images/Background/background-of-4.png",
      delay: 0.3,
    },
  ];

  return (
    <>
      {/* Enhanced Services Section */}
      <section className={`${isMobile ? 'py-20' : 'py-32'} text-white relative bg-black overflow-hidden`}>
        {/* Enhanced Background Effects */}
        <div className="absolute inset-0 bg-gradient-to-b from-black via-purple-950/10 to-black"></div>
        <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,rgba(147,51,234,0.1),transparent_70%)]"></div>

        {/* Animated Background Elements - Optimized for mobile */}
        {!isMobile && (
          <div className="absolute inset-0 overflow-hidden">
            <motion.div
              className="absolute w-96 h-96 rounded-full bg-purple-500/5 blur-3xl"
              style={{ top: '10%', left: '10%' }}
              animate={{
                opacity: [0.3, 0.6, 0.3],
                scale: [1, 1.2, 1],
                rotate: [0, 180, 360],
              }}
              transition={{
                duration: 20,
                repeat: Infinity,
                ease: "linear"
              }}
            />
            <motion.div
              className="absolute w-64 h-64 rounded-full bg-indigo-500/5 blur-3xl"
              style={{ bottom: '20%', right: '15%' }}
              animate={{
                opacity: [0.2, 0.5, 0.2],
                scale: [1, 1.3, 1],
                rotate: [360, 180, 0],
              }}
              transition={{
                duration: 25,
                repeat: Infinity,
                ease: "linear"
              }}
            />
          </div>
        )}

        <MobileOptimizedContainer spacing="normal" maxWidth="xl" className="relative z-10">
          {/* Enhanced Section Header */}
          <motion.div
            className={`text-center ${isMobile ? 'mb-12' : 'mb-20'}`}
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, ease: [0.22, 1, 0.36, 1] }}
            viewport={{ once: true }}
          >
            <motion.div
              className="inline-block mb-6"
              initial={{ width: 0 }}
              whileInView={{ width: 100 }}
              transition={{ duration: 1, delay: 0.3 }}
              viewport={{ once: true }}
            >
              <div className="h-0.5 bg-gradient-to-r from-transparent via-purple-500 to-transparent"></div>
            </motion.div>

            <MobileText
              variant="h2"
              className="font-bold mb-4 sm:mb-6 bg-gradient-to-r from-white via-purple-100 to-white bg-clip-text text-transparent leading-tight"
            >
              {t('services.comprehensiveSupport')}
              <span className="block bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
                {t('services.forEntrepreneurs')}
              </span>
            </MobileText>

            <motion.p
              className={`${isMobile ? 'text-base' : 'text-xl'} text-gray-300 max-w-3xl mx-auto leading-relaxed px-4`}
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              viewport={{ once: true }}
            >
              {t('services.comprehensiveDescription')}
            </motion.p>

            <motion.div
              className="mt-6 h-0.5 w-20 bg-gradient-to-r from-transparent via-purple-500 to-transparent mx-auto"
              initial={{ width: 0 }}
              whileInView={{ width: 80 }}
              transition={{ duration: 1, delay: 0.6 }}
              viewport={{ once: true }}
            />
          </motion.div>

          {/* Enhanced Services Grid - Mobile Optimized */}
          <MobileGrid
            cols={{ mobile: 1, tablet: 1, desktop: 2 }}
            gap={isMobile ? "md" : "lg"}
            className="max-w-7xl mx-auto"
          >
            {servicesData.map((service, index) => (
              <motion.div
                key={`service-container-${service.title}`}
                className="group"
                initial={{ opacity: 0, y: 40 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{
                  duration: 0.8,
                  delay: index * 0.15,
                  ease: [0.22, 1, 0.36, 1]
                }}
                viewport={{ once: true, margin: "-50px" }}
                whileHover={isMobile ? {} : { y: -8 }}
              >
                <ServiceCard
                  title={service.title}
                  description={service.description}
                  href={service.href}
                  imageUrl={service.imageUrl}
                  delay={0} // Remove delay since we're handling it in the container
                />
              </motion.div>
            ))}
          </MobileGrid>

          {/* Enhanced Call-to-Action */}
          <motion.div
            className={`text-center ${isMobile ? 'mt-12' : 'mt-20'}`}
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            viewport={{ once: true }}
          >
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              transition={{ type: "spring", stiffness: 400, damping: 17 }}
            >
              <motion.button
                className="relative overflow-hidden rounded-full bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-500 hover:to-indigo-500 text-white border-0 px-8 py-4 text-lg font-semibold shadow-2xl shadow-purple-500/25 group"
                whileHover={{ boxShadow: "0 25px 50px rgba(147, 51, 234, 0.4)" }}
              >
                <span className="relative z-10">{t('services.exploreAllServices')}</span>
                <motion.div
                  className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent"
                  initial={{ x: "-100%" }}
                  whileHover={{ x: "100%" }}
                  transition={{ duration: 0.6 }}
                />
              </motion.button>
            </motion.div>
          </motion.div>
        </MobileOptimizedContainer>
      </section>

      <section className="bg-black text-white py-32 relative overflow-hidden">
        <ImageAutoSlider />
      </section>
    </>
  );
};

export default ServiceIntroductionSection;

'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { BokehBackground, GlassCard } from '@/components/ui/aceternity/bokeh-background';
import { StarsBackground } from '@/components/ui/stars-background';
import { ShootingStars } from '@/components/ui/shooting-stars';
import { useLanguage } from '@/lib/context/language-context';
import {
  BookOpen,
  Users,
  Award,
  ArrowRight,
  Sparkles,
  Target,
  Brain,
  Rocket,
  Star
} from 'lucide-react';

const features = [
  {
    icon: <Brain className="h-8 w-8" />,
    titleKey: 'courses.welcome.feature1.title',
    descKey: 'courses.welcome.feature1.desc',
    color: 'text-purple-400',
    gradient: 'from-purple-500/20 to-violet-500/20',
    accentColor: '#8b5cf6'
  },
  {
    icon: <Target className="h-8 w-8" />,
    titleKey: 'courses.welcome.feature2.title',
    descKey: 'courses.welcome.feature2.desc',
    color: 'text-cyan-400',
    gradient: 'from-cyan-500/20 to-blue-500/20',
    accentColor: '#06b6d4'
  },
  {
    icon: <Users className="h-8 w-8" />,
    titleKey: 'courses.welcome.feature3.title',
    descKey: 'courses.welcome.feature3.desc',
    color: 'text-pink-400',
    gradient: 'from-pink-500/20 to-rose-500/20',
    accentColor: '#ec4899'
  },
  {
    icon: <Award className="h-8 w-8" />,
    titleKey: 'courses.welcome.feature4.title',
    descKey: 'courses.welcome.feature4.desc',
    color: 'text-amber-400',
    gradient: 'from-amber-500/20 to-orange-500/20',
    accentColor: '#f59e0b'
  }
];

export default function CoursesWelcomePage() {
  const { t, isLoaded } = useLanguage();

  if (!isLoaded) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="animate-pulse">
          <div className="h-8 w-32 bg-primary/20 rounded-full mb-4"></div>
          <div className="h-4 w-48 bg-gray-700/50 rounded mb-2"></div>
          <div className="h-4 w-40 bg-gray-700/50 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <BokehBackground
      className="min-h-screen"
      density={40}
      speed={2.8}
      colors={['#9333ea', '#7c3aed', '#6366f1', '#8b5cf6', '#a855f7']}
    >
      <StarsBackground
        starDensity={0.00018}
        allStarsTwinkle={true}
        twinkleProbability={0.7}
        minTwinkleSpeed={0.9}
        maxTwinkleSpeed={1.6}
      />
      <ShootingStars
        minSpeed={15}
        maxSpeed={35}
        minDelay={1800}
        maxDelay={4500}
        starColor="#9333ea"
        trailColor="#7c3aed"
      />

      <div className="container mx-auto px-4 max-w-7xl relative z-20 py-20">
        {/* Enhanced Cosmic Hero Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-20 relative"
        >
          {/* Floating cosmic elements */}
          <div className="absolute inset-0 pointer-events-none">
            {Array.from({ length: 6 }).map((_, i) => (
              <motion.div
                key={`cosmic-element-${i}`}
                className="absolute w-1 h-1 bg-purple-400/60 rounded-full"
                style={{
                  left: `${15 + i * 12}%`,
                  top: `${20 + (i % 3) * 25}%`,
                }}
                animate={{
                  opacity: [0.3, 0.9, 0.3],
                  scale: [0.8, 1.4, 0.8],
                  y: [-10, 10, -10],
                }}
                transition={{
                  duration: 4,
                  repeat: Infinity,
                  delay: i * 0.7,
                  ease: "easeInOut"
                }}
              />
            ))}
          </div>

          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="flex items-center justify-center mb-8"
          >
            <div className="relative">
              <motion.div
                className="absolute inset-0 rounded-full bg-gradient-to-r from-purple-500/30 to-pink-500/30 blur-xl"
                animate={{
                  scale: [1, 1.2, 1],
                  opacity: [0.5, 0.8, 0.5],
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
              />
              <div className="relative rounded-full bg-gradient-to-br from-purple-500/20 to-pink-500/20 backdrop-blur-md border border-purple-500/30 p-6">
                <BookOpen className="h-16 w-16 text-purple-400" />
              </div>
            </div>
          </motion.div>

          <motion.h1
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="text-5xl md:text-7xl font-bold mb-8 bg-gradient-to-r from-white via-purple-200 to-purple-400 bg-clip-text text-transparent leading-tight"
          >
            {t('courses.welcome.title') || 'Welcome to InnoHub Courses'}
          </motion.h1>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            className="text-xl text-white/80 mb-10 max-w-4xl mx-auto leading-relaxed"
          >
            {t('courses.welcome.subtitle') || 'Discover your talents, unlock your potential, and accelerate your entrepreneurial journey with personalized learning experiences.'}
          </motion.p>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.8 }}
            className="flex flex-col sm:flex-row gap-6 justify-center"
          >
            <Link href="/auth/login">
              <Button
                size="lg"
                className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white border-0 px-8 py-4 rounded-full text-lg font-semibold shadow-lg hover:shadow-purple-500/25 transition-all duration-300 flex items-center gap-3"
              >
                <Rocket className="h-5 w-5" />
                {t('courses.welcome.getStarted') || 'Get Started'}
                <ArrowRight className="h-5 w-5" />
              </Button>
            </Link>

            <Link href="/about">
              <Button
                variant="outline"
                size="lg"
                className="border-purple-500/40 hover:bg-purple-500/10 text-white backdrop-blur-sm px-8 py-4 rounded-full text-lg font-semibold transition-all duration-300"
              >
                {t('courses.welcome.learnMore') || 'Learn More'}
              </Button>
            </Link>
          </motion.div>
        </motion.div>

        {/* Enhanced Features Grid */}
        <motion.div
          initial={{ opacity: 0, y: 40 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1.0, duration: 0.8 }}
          className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-20"
        >
          {features.map((feature, index) => (
            <motion.div
              key={`feature-${index}`}
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 1.1 + index * 0.15, duration: 0.6 }}
              whileHover={{ y: -8, transition: { duration: 0.3 } }}
            >
              <GlassCard className="h-full p-1 group">
                <div className="p-6 h-full flex flex-col items-center text-center relative">
                  {/* Gradient overlay */}
                  <div className={`absolute inset-0 bg-gradient-to-br ${feature.gradient} opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl`} />

                  {/* Icon with enhanced styling */}
                  <motion.div
                    className="relative mb-6"
                    whileHover={{ scale: 1.1, rotate: 5 }}
                    transition={{ duration: 0.3 }}
                  >
                    <div className="relative">
                      <motion.div
                        className={`absolute inset-0 rounded-full bg-gradient-to-r ${feature.gradient} blur-lg opacity-50`}
                        animate={{
                          scale: [1, 1.2, 1],
                          opacity: [0.3, 0.6, 0.3],
                        }}
                        transition={{
                          duration: 3,
                          repeat: Infinity,
                          delay: index * 0.5,
                          ease: "easeInOut"
                        }}
                      />
                      <div className={`relative w-16 h-16 rounded-full bg-gradient-to-br ${feature.gradient} backdrop-blur-sm border border-white/20 flex items-center justify-center`}>
                        <div className={`${feature.color}`}>
                          {feature.icon}
                        </div>
                      </div>
                    </div>
                  </motion.div>

                  <h3 className="text-xl font-bold text-white mb-4 relative z-10">
                    {t(feature.titleKey) || 'Feature Title'}
                  </h3>

                  <p className="text-white/80 text-sm leading-relaxed relative z-10 flex-1">
                    {t(feature.descKey) || 'Feature description'}
                  </p>

                  {/* Bottom accent */}
                  <motion.div
                    className={`absolute bottom-0 left-0 h-1 bg-gradient-to-r ${feature.gradient} rounded-b-xl w-0 group-hover:w-full transition-all duration-500`}
                    initial={false}
                  />
                </div>
              </GlassCard>
            </motion.div>
          ))}
        </motion.div>

        {/* Enhanced Process Steps */}
        <motion.div
          initial={{ opacity: 0, y: 40 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1.5, duration: 0.8 }}
          className="mb-20 relative"
        >
          {/* Background cosmic elements */}
          <div className="absolute inset-0 pointer-events-none">
            {Array.from({ length: 3 }).map((_, i) => (
              <motion.div
                key={`process-cosmic-${i}`}
                className="absolute w-1.5 h-1.5 bg-purple-400/20 rounded-full"
                style={{
                  left: `${25 + i * 25}%`,
                  top: `${30 + i * 20}%`,
                }}
                animate={{
                  opacity: [0.2, 0.6, 0.2],
                  scale: [0.8, 1.5, 0.8],
                  y: [-15, 15, -15],
                }}
                transition={{
                  duration: 4,
                  repeat: Infinity,
                  delay: i * 1.2,
                  ease: "easeInOut"
                }}
              />
            ))}
          </div>

          <div className="text-center mb-16 relative z-10">
            <motion.h2
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 1.6, duration: 0.6 }}
              className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-white via-purple-200 to-purple-400 bg-clip-text text-transparent"
            >
              {t('courses.welcome.howItWorks') || 'How It Works'}
            </motion.h2>
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 1.7, duration: 0.6 }}
              className="text-white/80 text-xl max-w-3xl mx-auto"
            >
              {t('courses.welcome.processDesc') || 'Your personalized learning journey in three simple steps'}
            </motion.p>
          </div>

          <div className="grid md:grid-cols-3 gap-12 relative z-10">
            {[1, 2, 3].map((step, index) => (
              <motion.div
                key={step}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 1.8 + index * 0.2, duration: 0.6 }}
                whileHover={{ y: -5, transition: { duration: 0.3 } }}
                className="text-center group"
              >
                <div className="relative mb-8">
                  {/* Connection line */}
                  {index < 2 && (
                    <div className="hidden md:block absolute top-1/2 left-full w-full h-0.5 bg-gradient-to-r from-purple-500/40 to-transparent -translate-y-1/2 z-0"></div>
                  )}

                  {/* Step circle with enhanced styling */}
                  <motion.div
                    className="relative w-20 h-20 mx-auto"
                    whileHover={{ scale: 1.1 }}
                    transition={{ duration: 0.3 }}
                  >
                    <motion.div
                      className="absolute inset-0 rounded-full bg-gradient-to-r from-purple-500/30 to-pink-500/30 blur-lg"
                      animate={{
                        scale: [1, 1.2, 1],
                        opacity: [0.5, 0.8, 0.5],
                      }}
                      transition={{
                        duration: 3,
                        repeat: Infinity,
                        delay: index * 0.5,
                        ease: "easeInOut"
                      }}
                    />
                    <div className="relative w-full h-full bg-gradient-to-br from-purple-500/20 to-pink-500/20 backdrop-blur-md border border-purple-500/30 rounded-full flex items-center justify-center group-hover:border-purple-400/50 transition-colors duration-300">
                      <span className="text-3xl font-bold text-purple-400 group-hover:text-purple-300 transition-colors duration-300">{step}</span>
                    </div>
                  </motion.div>
                </div>

                <h3 className="text-2xl font-bold text-white mb-4 group-hover:text-purple-300 transition-colors duration-300">
                  {t(`courses.welcome.step${step}.title`) || `Step ${step}`}
                </h3>
                <p className="text-white/70 text-lg leading-relaxed">
                  {t(`courses.welcome.step${step}.desc`) || 'Step description'}
                </p>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Enhanced CTA Section */}
        <motion.div
          initial={{ opacity: 0, y: 40 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 2.2, duration: 0.8 }}
          className="relative"
        >
          {/* Background cosmic elements */}
          <div className="absolute inset-0 pointer-events-none">
            {Array.from({ length: 4 }).map((_, i) => (
              <motion.div
                key={`cta-cosmic-${i}`}
                className="absolute w-1 h-1 bg-purple-400/40 rounded-full"
                style={{
                  left: `${20 + i * 20}%`,
                  top: `${25 + (i % 2) * 50}%`,
                }}
                animate={{
                  opacity: [0.3, 0.8, 0.3],
                  scale: [0.8, 1.6, 0.8],
                  rotate: [0, 180, 360],
                }}
                transition={{
                  duration: 5,
                  repeat: Infinity,
                  delay: i * 0.8,
                  ease: "easeInOut"
                }}
              />
            ))}
          </div>

          <GlassCard className="p-1 relative overflow-hidden group">
            {/* Enhanced gradient overlay */}
            <div className="absolute inset-0 bg-gradient-to-br from-purple-500/10 via-pink-500/10 to-blue-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

            <div className="text-center py-16 px-8 relative z-10">
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 2.3, duration: 0.6 }}
                className="flex items-center justify-center mb-8"
              >
                <div className="relative">
                  <motion.div
                    className="absolute inset-0 rounded-full bg-gradient-to-r from-purple-500/40 to-pink-500/40 blur-xl"
                    animate={{
                      scale: [1, 1.3, 1],
                      opacity: [0.4, 0.7, 0.4],
                      rotate: [0, 180, 360],
                    }}
                    transition={{
                      duration: 4,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                  />
                  <div className="relative rounded-full bg-gradient-to-br from-purple-500/20 to-pink-500/20 backdrop-blur-md border border-purple-500/30 p-4">
                    <Sparkles className="h-10 w-10 text-purple-400" />
                  </div>
                </div>
              </motion.div>

              <motion.h2
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 2.4, duration: 0.6 }}
                className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-white via-purple-200 to-purple-400 bg-clip-text text-transparent"
              >
                {t('courses.welcome.readyToStart') || 'Ready to Start Your Journey?'}
              </motion.h2>

              <motion.p
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 2.5, duration: 0.6 }}
                className="text-white/80 text-xl mb-10 max-w-3xl mx-auto leading-relaxed"
              >
                {t('courses.welcome.ctaDesc') || 'Join thousands of entrepreneurs who have transformed their ideas into successful businesses with InnoHub.'}
              </motion.p>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 2.6, duration: 0.6 }}
                className="flex flex-col sm:flex-row gap-6 justify-center"
              >
                <Link href="/auth/signup">
                  <Button
                    size="lg"
                    className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white border-0 px-10 py-4 rounded-full text-lg font-semibold shadow-lg hover:shadow-purple-500/25 transition-all duration-300 flex items-center gap-3"
                  >
                    <Star className="h-5 w-5" />
                    {t('courses.welcome.joinNow') || 'Join Now'}
                  </Button>
                </Link>

                <Link href="/auth/login">
                  <Button
                    variant="outline"
                    size="lg"
                    className="border-purple-500/40 hover:bg-purple-500/10 text-white backdrop-blur-sm px-10 py-4 rounded-full text-lg font-semibold transition-all duration-300"
                  >
                    {t('courses.welcome.signIn') || 'Sign In'}
                  </Button>
                </Link>
              </motion.div>
            </div>

            {/* Bottom accent line */}
            <motion.div
              className="absolute bottom-0 left-0 h-1 bg-gradient-to-r from-purple-500 via-pink-500 to-blue-500 w-0 group-hover:w-full transition-all duration-700"
              initial={false}
            />
          </GlassCard>
        </motion.div>
      </div>
    </BokehBackground>
  );
}
